# Copyright (c) Facebook, Inc. and its affiliates.
#
# This source code is licensed under the MIT license found in the
# LICENSE file in the root directory of this source tree.

import gc
import torch
import logging
from typing import Optional, Dict, Any

logger = logging.getLogger(__name__)


class MemoryManager:
    """内存管理工具类，用于优化训练过程中的内存使用"""
    
    def __init__(self, enable_cuda_cache_cleanup=True, cleanup_interval=100):
        self.enable_cuda_cache_cleanup = enable_cuda_cache_cleanup
        self.cleanup_interval = cleanup_interval
        self.step_count = 0
        self.peak_memory = 0
        
    def step(self):
        """每个训练步骤调用，进行内存管理"""
        self.step_count += 1
        
        if torch.cuda.is_available():
            current_memory = torch.cuda.memory_allocated()
            self.peak_memory = max(self.peak_memory, current_memory)
            
            # 定期清理CUDA缓存
            if (self.enable_cuda_cache_cleanup and 
                self.step_count % self.cleanup_interval == 0):
                self.cleanup_cuda_cache()
                
        # 定期进行垃圾回收
        if self.step_count % (self.cleanup_interval * 2) == 0:
            self.cleanup_python_memory()
    
    def cleanup_cuda_cache(self):
        """清理CUDA缓存"""
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            logger.info(f"CUDA cache cleared at step {self.step_count}")
    
    def cleanup_python_memory(self):
        """清理Python内存"""
        collected = gc.collect()
        logger.info(f"Python GC collected {collected} objects at step {self.step_count}")
    
    def get_memory_stats(self) -> Dict[str, Any]:
        """获取内存统计信息"""
        stats = {
            "step_count": self.step_count,
            "peak_memory_mb": self.peak_memory / 1024 / 1024 if self.peak_memory > 0 else 0,
        }
        
        if torch.cuda.is_available():
            stats.update({
                "cuda_allocated_mb": torch.cuda.memory_allocated() / 1024 / 1024,
                "cuda_cached_mb": torch.cuda.memory_reserved() / 1024 / 1024,
                "cuda_max_allocated_mb": torch.cuda.max_memory_allocated() / 1024 / 1024,
            })
        
        return stats
    
    def reset_peak_memory_stats(self):
        """重置峰值内存统计"""
        if torch.cuda.is_available():
            torch.cuda.reset_peak_memory_stats()
        self.peak_memory = 0


def optimize_model_memory(model):
    """优化模型内存使用"""
    # 清理模型中可能的缓存
    if hasattr(model, 'clear_cache'):
        model.clear_cache()
    
    # 对于包含LRU缓存的数据集，清理缓存
    def clear_dataset_cache(module):
        if hasattr(module, 'clear_cache'):
            module.clear_cache()
        for child in module.children():
            clear_dataset_cache(child)
    
    clear_dataset_cache(model)


def cleanup_logging_outputs(logging_outputs, max_size=10):
    """清理logging_outputs，防止内存累积"""
    if len(logging_outputs) > max_size:
        # 只保留最近的几个输出
        return logging_outputs[-max_size:]
    return logging_outputs


def safe_del(*variables):
    """安全删除变量"""
    for var in variables:
        try:
            if var is not None:
                del var
        except:
            pass


class MemoryEfficientTrainingMixin:
    """训练器的内存优化混入类"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.memory_manager = MemoryManager()
    
    def train_step_with_memory_optimization(self, samples, raise_oom=False):
        """带内存优化的训练步骤"""
        # 在训练步骤开始时进行内存管理
        self.memory_manager.step()
        
        # 调用原始的训练步骤
        result = super().train_step(samples, raise_oom)
        
        # 训练步骤结束后清理内存
        if hasattr(self, 'model'):
            optimize_model_memory(self.model)
        
        return result
    
    def get_memory_stats(self):
        """获取内存统计信息"""
        return self.memory_manager.get_memory_stats()


def apply_memory_optimizations(trainer):
    """为训练器应用内存优化"""
    # 创建内存管理器
    if not hasattr(trainer, 'memory_manager'):
        trainer.memory_manager = MemoryManager()
    
    # 包装原始的train_step方法
    original_train_step = trainer.train_step
    
    def optimized_train_step(samples, raise_oom=False):
        # 内存管理
        trainer.memory_manager.step()
        
        # 执行原始训练步骤
        result = original_train_step(samples, raise_oom)
        
        # 后处理内存优化
        if hasattr(trainer, 'model'):
            optimize_model_memory(trainer.model)
        
        return result
    
    trainer.train_step = optimized_train_step
    return trainer


# 全局内存管理器实例
global_memory_manager = MemoryManager()


def get_global_memory_manager():
    """获取全局内存管理器"""
    return global_memory_manager


def log_memory_usage(prefix="", level=logging.INFO):
    """记录内存使用情况"""
    stats = global_memory_manager.get_memory_stats()
    msg = f"{prefix} Memory Stats: " + ", ".join([f"{k}={v:.2f}" if isinstance(v, float) else f"{k}={v}" for k, v in stats.items()])
    logger.log(level, msg)
