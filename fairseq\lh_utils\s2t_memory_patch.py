#!/usr/bin/env python3
"""
Speech-to-Text 内存优化补丁
解决训练过程中的内存泄漏问题

主要问题：
1. logging_outputs 累积导致内存泄漏
2. metrics aggregators 过多导致内存泄漏  
3. 中间变量未及时释放
4. CUDA缓存未定期清理
5. 数据集缓存过大

使用方法：
在训练脚本开始时调用 apply_memory_patches()
"""

import gc
import torch
import logging
from typing import Dict, Any, List, Optional
from functools import wraps

logger = logging.getLogger(__name__)


def memory_efficient_train_step(original_train_step):
    """装饰器：为train_step添加内存优化"""
    @wraps(original_train_step)
    def wrapper(self, samples, raise_oom=False):
        # 执行原始训练步骤
        result = original_train_step(self, samples, raise_oom)
        
        # 定期清理CUDA缓存
        num_updates = self.get_num_updates()
        if torch.cuda.is_available() and num_updates % 50 == 0:
            logger.info(f"{num_updates} % 50 == 0 清理CUDA缓存")
            torch.cuda.empty_cache()
            
        # 定期进行垃圾回收
        if num_updates % 200 == 0:
            collected = gc.collect()
            if collected > 0:
                logger.info(f"GC collected {collected} objects at step {num_updates}")
        
        return result
    
    return wrapper


def memory_efficient_logging_outputs(original_method):
    """装饰器：优化logging_outputs处理"""
    @wraps(original_method)
    def wrapper(self, logging_outputs, *args, **kwargs):
        # 限制logging_outputs的大小
        if isinstance(logging_outputs, list) and len(logging_outputs) > 20:
            logging_outputs = logging_outputs[-20:]
        
        result = original_method(self, logging_outputs, *args, **kwargs)
        
        # 清理logging_outputs
        if isinstance(logging_outputs, list):
            logging_outputs.clear()
        
        return result
    
    return wrapper


def patch_trainer_memory_issues():
    """修补Trainer类的内存问题"""
    from fairseq.trainer import Trainer
    
    # 修补train_step方法
    if hasattr(Trainer, 'train_step'):
        original_train_step = Trainer.train_step
        Trainer.train_step = memory_efficient_train_step(original_train_step)
        logger.info("Patched Trainer.train_step for memory efficiency")
    
    # 修补_reduce_and_log_stats方法
    if hasattr(Trainer, '_reduce_and_log_stats'):
        original_method = Trainer._reduce_and_log_stats
        Trainer._reduce_and_log_stats = memory_efficient_logging_outputs(original_method)
        logger.info("Patched Trainer._reduce_and_log_stats for memory efficiency")


def patch_metrics_memory_issues():
    """修补metrics模块的内存问题"""
    from fairseq.logging import metrics
    
    # 添加定期清理功能
    original_log_scalar = metrics.log_scalar
    
    def memory_efficient_log_scalar(key, value, weight=1, priority=10, round=None):
        result = original_log_scalar(key, value, weight, priority, round)
        
        # 定期清理metrics
        if len(metrics._aggregators) > 100:
            metrics.periodic_cleanup(max_aggregators=50)
        
        return result
    
    metrics.log_scalar = memory_efficient_log_scalar
    logger.info("Patched metrics.log_scalar for memory efficiency")


def patch_dataset_memory_issues():
    """修补数据集相关的内存问题"""
    try:
        from fairseq.data.lru_cache_dataset import LRUCacheDataset
        
        # 为LRUCacheDataset添加自动清理
        original_getitem = LRUCacheDataset.__getitem__
        
        def memory_efficient_getitem(self, index):
            result = original_getitem(self, index)
            
            # 每1000次访问清理一次缓存
            if not hasattr(self, '_access_count'):
                self._access_count = 0
            self._access_count += 1
            
            if self._access_count % 1000 == 0:
                if hasattr(self, 'clear_cache'):
                    self.clear_cache()
            
            return result
        
        LRUCacheDataset.__getitem__ = memory_efficient_getitem
        logger.info("Patched LRUCacheDataset for memory efficiency")
        
    except ImportError:
        logger.warning("Could not patch LRUCacheDataset - module not found")


def patch_speech_to_text_model():
    """修补speech_to_text模型的内存问题"""
    try:
        from fairseq.models.speech_to_text.s2t_transformer import S2TTransformerModel
        
        original_forward = S2TTransformerModel.forward
        
        def memory_efficient_forward(self, src_tokens, src_lengths, prev_output_tokens):
            # 执行原始forward
            result = original_forward(self, src_tokens, src_lengths, prev_output_tokens)
            
            # 清理中间变量
            if hasattr(self, '_intermediate_cache'):
                self._intermediate_cache.clear()
            
            return result
        
        S2TTransformerModel.forward = memory_efficient_forward
        logger.info("Patched S2TTransformerModel.forward for memory efficiency")
        
    except ImportError:
        logger.warning("Could not patch S2TTransformerModel - module not found")


def configure_memory_efficient_training():
    """配置内存高效的训练参数"""
    import os

    # 设置PyTorch内存管理
    if torch.cuda.is_available():
        # 启用内存池
        torch.cuda.empty_cache()

        # 设置内存分配策略
        os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'max_split_size_mb:128'

    # 设置垃圾回收
    gc.set_threshold(700, 10, 10)  # 更频繁的垃圾回收

    logger.info("Configured memory efficient training parameters")


def apply_memory_patches():
    """应用所有内存优化补丁"""
    logger.info("Applying memory optimization patches for speech-to-text training...")
    
    # 配置内存高效训练
    configure_memory_efficient_training()
    
    # 应用各种补丁
    patch_trainer_memory_issues()
    patch_metrics_memory_issues() 
    patch_dataset_memory_issues()
    patch_speech_to_text_model()
    
    logger.info("All memory optimization patches applied successfully!")


def monitor_memory_usage(step_interval=100):
    """内存使用监控装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            if hasattr(wrapper, '_step_count'):
                wrapper._step_count += 1
            else:
                wrapper._step_count = 1
            
            result = func(*args, **kwargs)
            
            if wrapper._step_count % step_interval == 0:
                if torch.cuda.is_available():
                    allocated = torch.cuda.memory_allocated() / 1024**3  # GB
                    cached = torch.cuda.memory_reserved() / 1024**3  # GB
                    logger.info(f"Step {wrapper._step_count}: GPU Memory - Allocated: {allocated:.2f}GB, Cached: {cached:.2f}GB")
            
            return result
        return wrapper
    return decorator


if __name__ == "__main__":
    # 可以直接运行此脚本来应用补丁
    apply_memory_patches()
    print("Memory optimization patches applied!")
